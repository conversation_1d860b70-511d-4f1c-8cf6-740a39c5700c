package com.example.habits9.di;

import com.example.habits9.data.HabitRepository;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.firestore.FirebaseFirestore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideHabitRepositoryFactory implements Factory<HabitRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  private final Provider<FirebaseAuth> authProvider;

  public DatabaseModule_ProvideHabitRepositoryFactory(Provider<FirebaseFirestore> firestoreProvider,
      Provider<FirebaseAuth> authProvider) {
    this.firestoreProvider = firestoreProvider;
    this.authProvider = authProvider;
  }

  @Override
  public HabitRepository get() {
    return provideHabitRepository(firestoreProvider.get(), authProvider.get());
  }

  public static DatabaseModule_ProvideHabitRepositoryFactory create(
      Provider<FirebaseFirestore> firestoreProvider, Provider<FirebaseAuth> authProvider) {
    return new DatabaseModule_ProvideHabitRepositoryFactory(firestoreProvider, authProvider);
  }

  public static HabitRepository provideHabitRepository(FirebaseFirestore firestore,
      FirebaseAuth auth) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideHabitRepository(firestore, auth));
  }
}
