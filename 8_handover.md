Design Brief: The Zenith Weekly Planning Experience
1. The Vision: What is Zenith?
Zenith is an intentional living system, not just a task manager. It’s designed to be a user's trusted partner in building a balanced, fulfilling life. Based on the "Agile Results" framework, its purpose is to guide users from chaos to clarity, helping them align their daily actions with their deepest goals.

2. Our Design Philosophy: The Feeling We Must Evoke
Evoke Calm, Not Anxiety. The UI must be a sanctuary. It should be minimalist, clean, and spacious. When a user opens the app, their heart rate should go down, not up. We are the antidote to the cluttered, overwhelming productivity app.

Guide, Don't Just Present. The workflow should feel like a wise, calm coach. It should ask thoughtful questions and guide the user through a reflective process, turning planning from a chore into a self-care ritual.

Reveal Insight, Not Just Data. The design must make the user feel understood. Through powerful visuals like the "Life Wheel," we provide instant, intuitive insights into their life's balance, helping them see patterns they never noticed before.

Feel Elegant & Premium. Every interaction, transition, and element should feel polished and deliberate. This is a high-quality tool for a high-quality life.

3. Key Mandates (Non-Negotiables)
The design must be built around these core concepts:

The 9 Life Aspects are the foundation of the entire system.

The Life Wheel is the central visual metaphor for balance.

The "Weekly 3" must be a distinct, high-priority step.

The Urgent/Uplifting Matrix is a core, interactive feature.

4. The User Journey: The Sunday Planning Workflow
We need a complete set of high-fidelity, mobile-first mockups for this critical user journey.

Step 1: The Gateway
Goal: To create a serene entry point into the planning ritual.

Design: A minimalist screen with an inspiring message ("Let's plan a week with intention.") and a single, elegant "Begin" button.

Step 2: The Reflection
Goal: To ground the user in self-awareness.

Design: Feature the Life Wheel from the previous week. Ask a simple, powerful question: "Where does your energy need to go this week?"

Step 3: The Commitment
Goal: To let the user choose their areas of focus.

Design: A clean, interactive list of the 9 Life Aspects. Tapping an aspect should fluidly expand to show the projects within it, with simple checkboxes for selection.

Step 4: The Focus
Goal: To establish the week's most critical priorities.

Design: A dedicated, focused screen for the "Weekly 3." The design should make these three outcomes feel significant and monumental.

Step 5: The Details
Goal: To translate broad projects into concrete outcomes.

Design: A frictionless interface for adding outcomes under each chosen project. The UI should make it effortless to add multiple items quickly.

Step 6: The Intention
Goal: To prioritize with feeling, not just logic.

Design: A highly visual, drag-and-drop interface for the Urgent/Uplifting Matrix. Users should physically move their outcomes into one of four quadrants. This interaction is key.

Step 7: The Confirmation
Goal: To provide a sense of completion and confidence.

Design: A beautiful summary screen. Showcase the "Weekly 3" at the top, the new Life Wheel reflecting the plan, and a final, satisfying "Commit to My Week" button that provides a sense of accomplishment.

5. Visual Direction
Primary Theme: Dark Mode. This supports our "Calm & Focused" philosophy. The palette should consist of deep blues or dark grays.

Accent Color: A single, vibrant, and optimistic accent color (like a bright teal, coral, or sun-yellow) for buttons, highlights, and the "uplifting" parts of the UI.

Typography: A premium, clean, and highly legible sans-serif font family. Establish a clear typographic scale.

Iconography: Minimalist, consistent, and elegant line icons.

6. Deliverables
High-fidelity mockups for all 7 steps of the workflow (mobile & desktop views).

A style guide detailing the color palette, typography, and iconography.