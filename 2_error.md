2025-08-09 21:06:20.956 28099-28099 HabitDetailsViewModel   com.example.uhabits_99               E  Error loading habit details for habitId: 1985792254 (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: 'awaitClose { yourCallbackOrListener.cancel() }' should be used in the end of callbackFlow block.
                                                                                                    Otherwise, a callback/listener may leak in case of external cancellation.
                                                                                                    See callbackFlow API documentation for the details.
                                                                                                    	at kotlinx.coroutines.flow.CallbackFlowBuilder.collectTo(Builders.kt:341)
                                                                                                    	at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:60)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.EventLoop.processUnconfinedEvent(EventLoop.common.kt:68)
                                                                                                    	at kotlinx.coroutines.DispatchedTaskKt.resumeUnconfined(DispatchedTask.kt:245)
                                                                                                    	at kotlinx.coroutines.DispatchedTaskKt.dispatch(DispatchedTask.kt:163)
                                                                                                    	at kotlinx.coroutines.CancellableContinuationImpl.dispatchResume(CancellableContinuationImpl.kt:474)
                                                                                                    	at kotlinx.coroutines.CancellableContinuationImpl.completeResume(CancellableContinuationImpl.kt:590)
                                                                                                    	at kotlinx.coroutines.channels.BufferedChannelKt.tryResume0(BufferedChannel.kt:2896)
                                                                                                    	at kotlinx.coroutines.channels.BufferedChannelKt.access$tryResume0(BufferedChannel.kt:1)
                                                                                                    	at kotlinx.coroutines.channels.BufferedChannel$BufferedChannelIterator.tryResumeHasNext(BufferedChannel.kt:1689)
                                                                                                    	at kotlinx.coroutines.channels.BufferedChannel.tryResumeReceiver(BufferedChannel.kt:642)
                                                                                                    	at kotlinx.coroutines.channels.BufferedChannel.updateCellSend(BufferedChannel.kt:458)
                                                                                                    	at kotlinx.coroutines.channels.BufferedChannel.access$updateCellSend(BufferedChannel.kt:36)
                                                                                                    	at kotlinx.coroutines.channels.BufferedChannel.trySend-JP2dKIU(BufferedChannel.kt:3299)
                                                                                                    	at kotlinx.coroutines.channels.ChannelCoroutine.trySend-JP2dKIU(Unknown Source:2)
                                                                                                    	at com.example.habits9.data.HabitRepository$getAllHabits$1.invokeSuspend$lambda$2(HabitRepository.kt:68)
                                                                                                    	at com.example.habits9.data.HabitRepository$getAllHabits$1.$r8$lambda$b_dzGUZ2U9YGx2T3EFw5WDl6r64(Unknown Source:0)
                                                                                                    	at com.example.habits9.data.HabitRepository$getAllHabits$1$$ExternalSyntheticLambda0.onEvent(D8$$SyntheticClass:0)
                                                                                                    	at com.google.firebase.firestore.Query.lambda$addSnapshotListenerInternal$3$com-google-firebase-firestore-Query(Query.java:1176)
                                                                                                    	at com.google.firebase.firestore.Query$$ExternalSyntheticLambda0.onEvent(D8$$SyntheticClass:0)
                                                                                                    	at com.google.firebase.firestore.core.AsyncEventListener.lambda$onEvent$0$com-google-firebase-firestore-core-AsyncEventListener(AsyncEventListener.java:42)
                                                                                                    	at com.google.firebase.firestore.core.AsyncEventListener$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:959)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:100)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:249)
                                                                                                    	at android.os.Looper.loop(Looper.java:337)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9500)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:636)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1005)
2025-08-09 21:06:20.986 28099-28099 HabitDetailsScreen      com.example.uhabits_99               E  Error loading habit: 'awaitClose { yourCallbackOrListener.cancel() }' should be used in the end of callbackFlow block.
                                                                                                    Otherwise, a callback/listener may leak in case of external cancellation.
                                                                                                    See callbackFlow API documentation for the details.
2025-08-09 21:06:21.027  2674-2674  ArtChoreographerMonitor com.android.systemui                 D  invoke error.
2025-08-09 21:06:21.141 30392-30529 SushiPublisher          com.amazon.kindle                    E  Error opening a connection to Sushi using endpoint https://unagi-na.amazon.com/1/events/com.amazon.eel.KindleFastMetrics.Prod.Android.fm
2025-08-09 21:06:21.540  2674-2674  ArtChoreographerMonitor com.android.systemui                 D  invoke error.
2025-08-09 21:06:23.145 30392-30529 SushiPublisher          com.amazon.kindle                    E  Error opening a connection to Sushi using endpoint https://unagi-na.amazon.com/1/events/com.amazon.eel.KindleFastMetrics.Prod.Android.fm
2025-08-09 21:06:24.844  2674-2674  ArtChoreographerMonitor com.android.systemui                 D  invoke error.
2025-08-09 21:06:25.285  2674-2674  ArtChoreographerMonitor com.android.systemui                 D  invoke error.
2025-08-09 21:06:27.152 30392-30529 SushiPublisher          com.amazon.kindle                    E  Error opening a connection to Sushi using endpoint https://unagi-na.amazon.com/1/events/com.amazon.eel.KindleFastMetrics.Prod.Android.fm
2025-08-09 21:06:30.762  2674-2674  ArtChoreographerMonitor com.android.systemui                 D  invoke error.
2025-08-09 21:06:30.973  2674-2674  ArtChoreographerMonitor com.android.systemui                 D  invoke error.
2025-08-09 21:06:33.780  2674-2674  ArtChoreographerMonitor com.android.systemui                 D  invoke error.
2025-08-09 21:06:33.981  2674-2674  ArtChoreographerMonitor com.android.systemui                 D  invoke error.
2025-08-09 21:06:36.815  2674-2674  ArtChoreographerMonitor com.android.systemui                 D  invoke error.
2025-08-09 21:06:37.016  2674-2674  ArtChoreographerMonitor com.android.systemui                 D  invoke error.
2025-08-09 21:06:39.858  2674-2674  ArtChoreographerMonitor com.android.systemui                 D  invoke error.
2025-08-09 21:06:40.071  2674-2674  ArtChoreographerMonitor com.android.systemui                 D  invoke error.