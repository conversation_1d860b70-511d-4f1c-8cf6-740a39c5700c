package com.example.habits9.ui.managesections;

import com.example.habits9.data.HabitSectionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ManageSectionsViewModel_Factory implements Factory<ManageSectionsViewModel> {
  private final Provider<HabitSectionRepository> habitSectionRepositoryProvider;

  public ManageSectionsViewModel_Factory(
      Provider<HabitSectionRepository> habitSectionRepositoryProvider) {
    this.habitSectionRepositoryProvider = habitSectionRepositoryProvider;
  }

  @Override
  public ManageSectionsViewModel get() {
    return newInstance(habitSectionRepositoryProvider.get());
  }

  public static ManageSectionsViewModel_Factory create(
      Provider<HabitSectionRepository> habitSectionRepositoryProvider) {
    return new ManageSectionsViewModel_Factory(habitSectionRepositoryProvider);
  }

  public static ManageSectionsViewModel newInstance(HabitSectionRepository habitSectionRepository) {
    return new ManageSectionsViewModel(habitSectionRepository);
  }
}
