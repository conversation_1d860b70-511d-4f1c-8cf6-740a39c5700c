# Prompt 5.2a: Make Habit Details Header Dynamic

## A. The Objective & Context

The goal of this task is to connect the existing static UI for the Habit Details header and subtitle (as seen in `64.jpg`) to the live data from the `ViewModel`. Currently, the screen shows the same information for all habits. This task will make it dynamic, ensuring it displays the correct information for the specific habit being viewed.

This is a targeted fix to complete the work from the previous prompt. No new UI elements should be added.

## B. Detailed Implementation Plan

### 1. Connect UI Elements to the ViewModel
- In the `ShowHabitActivity`, you must get a reference to the `ViewModel` that provides the habit details.
- **Observe LiveData**: The ViewModel already exposes the necessary data. Set up observers for each of the following data points:
    - Habit Name
    - Habit Frequency
    - Current Week String
    - Current Date String
- **Update UI**: Inside each observer, update the corresponding UI element with the new data.
    - Set the **Toolbar's title** with the habit's name.
    - Set the text of the **three `TextViews`** in the subtitle bar with the frequency, week, and date strings.

## C. Meticulous Verification Plan

1.  **Dynamic Data Verification**:
    - **CRITICAL**: Navigate to the details screen for a habit named "Read a Book." Verify the toolbar title correctly says "Read a Book."
    - Go back and navigate to the details screen for a different habit, like "Go for a run." CRITICAL: The toolbar title must now say "Go for a run."
    - For each habit, verify that the frequency, week, and date displayed in the subtitle are all correct.

2.  **State Change Verification**:
    - If possible, edit a habit's name and save it. When you return to the details screen, the title in the toolbar should reflect the new name without requiring an app restart.

3.  **No Visual Change Verification**:
    - Confirm that no visual layout changes have occurred. The UI should still look exactly like `64.jpg`, but now with real data.

## D. Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.