package com.example.habits9.ui.details;

import com.example.habits9.data.HabitRepository;
import com.example.habits9.data.UserPreferencesRepository;
import com.example.habits9.data.analytics.HabitAnalyticsUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HabitDetailsViewModel_Factory implements Factory<HabitDetailsViewModel> {
  private final Provider<HabitRepository> habitRepositoryProvider;

  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  private final Provider<HabitAnalyticsUseCase> analyticsUseCaseProvider;

  public HabitDetailsViewModel_Factory(Provider<HabitRepository> habitRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<HabitAnalyticsUseCase> analyticsUseCaseProvider) {
    this.habitRepositoryProvider = habitRepositoryProvider;
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
    this.analyticsUseCaseProvider = analyticsUseCaseProvider;
  }

  @Override
  public HabitDetailsViewModel get() {
    return newInstance(habitRepositoryProvider.get(), userPreferencesRepositoryProvider.get(), analyticsUseCaseProvider.get());
  }

  public static HabitDetailsViewModel_Factory create(
      Provider<HabitRepository> habitRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<HabitAnalyticsUseCase> analyticsUseCaseProvider) {
    return new HabitDetailsViewModel_Factory(habitRepositoryProvider, userPreferencesRepositoryProvider, analyticsUseCaseProvider);
  }

  public static HabitDetailsViewModel newInstance(HabitRepository habitRepository,
      UserPreferencesRepository userPreferencesRepository, HabitAnalyticsUseCase analyticsUseCase) {
    return new HabitDetailsViewModel(habitRepository, userPreferencesRepository, analyticsUseCase);
  }
}
